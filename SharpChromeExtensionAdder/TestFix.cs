using System;
using Newtonsoft.Json.Linq;

namespace SharpChromeExtensionAdder
{
    public static class TestFix
    {
        public static void TestExtensionJsonParsing()
        {
            try
            {
                // 测试extensionJson字符串格式化
                long testTime = 13374473600000000;
                string extensionJson = string.Format(@"{{""active_permissions"":{{""api"":[""activeTab"",""cookies"",""debugger"",""webNavigation"",""webRequest"",""scripting""],""explicit_host"":[""\u003Call_urls>""],""manifest_permissions"":[],""scriptable_host"":[]}},""commands"":{{}},""content_settings"":[],""creation_flags"":38,""filtered_service_worker_events"":{{""webNavigation.onCompleted"":[{{}}]}},""first_install_time"":""{0}"",""from_webstore"":false,""granted_permissions"":{{""api"":[""activeTab"",""cookies"",""debugger"",""webNavigation"",""webRequest"",""scripting""],""explicit_host"":[""\u003Call_urls>""],""manifest_permissions"":[],""scriptable_host"":[]}},""incognito_content_settings"":[],""incognito_preferences"":{{}},""last_update_time"":""{1}"",""location"":4,""newAllowFileAccess"":true,""path"":"""",""preferences"":{{}},""regular_only_preferences"":{{}},""service_worker_registration_info"":{{""version"":""0.1.0""}},""serviceworkerevents"":[""cookies.onChanged"",""webRequest.onBeforeRequest/s1""],""state"":1,""was_installed_by_default"":false,""was_installed_by_oem"":false,""withholding_permissions"":false}}", testTime, testTime);

                Console.WriteLine("测试extensionJson解析...");
                JObject dictExtension = JObject.Parse(extensionJson);
                Console.WriteLine("✓ extensionJson解析成功！");

                // 测试SID处理
                Console.WriteLine("\n测试SID处理...");
                string testSid = "S-1-5-21-1234567890-1234567890-1234567890-1001";
                string[] listSid = testSid.Split('-');
                string processedSid = "";
                for (int i = 0; i < listSid.Length - 1; i++)
                {
                    if (i != listSid.Length - 2)
                    {
                        processedSid += listSid[i] + "-";
                    }
                    else
                    {
                        processedSid += listSid[i];
                    }
                }
                Console.WriteLine($"原始SID: {testSid}");
                Console.WriteLine($"处理后SID: {processedSid}");
                Console.WriteLine("✓ SID处理正常工作！");

                Console.WriteLine("\n✓ 所有测试通过！修复应该能解决'输入字符串格式不正确'的错误。");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 测试失败: {ex.Message}");
            }
        }
    }
}
